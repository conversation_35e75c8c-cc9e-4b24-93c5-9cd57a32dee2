/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { ArienCore } from '../index.js';
import { ChatMessage, AuthConfig } from '../types/index.js';

// Mock the dependencies
jest.mock('../config/index.js', () => ({
  configManager: {
    setAuthConfig: jest.fn(),
    getAuthConfig: jest.fn(),
    isProviderAuthenticated: jest.fn(),
    getCurrentProvider: jest.fn(),
    getCurrentModel: jest.fn(),
    setCurrentProvider: jest.fn(),
    getTheme: jest.fn().mockReturnValue('cyberpunk'),
    setTheme: jest.fn(),
    getSystemPrompt: jest.fn().mockReturnValue('Default system prompt'),
    setSystemPrompt: jest.fn(),
    addChatSession: jest.fn(),
    getChatHistory: jest.fn().mockReturnValue([]),
    clearChatHistory: jest.fn(),
    reset: jest.fn()
  }
}));

jest.mock('../providers/index.js', () => ({
  providerManager: {
    getAllProviders: jest.fn().mockReturnValue(new Map()),
    createProvider: jest.fn(),
    getProvider: jest.fn(),
    removeProvider: jest.fn()
  }
}));

jest.mock('../functions/index.js', () => ({
  functionManager: {
    getAllFunctions: jest.fn().mockReturnValue([])
  }
}));

describe('ArienCore', () => {
  let arienCore: ArienCore;
  let mockConfigManager: any;
  let mockProviderManager: any;
  let mockFunctionManager: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Get mocked modules
    const { configManager } = require('../config/index.js');
    const { providerManager } = require('../providers/index.js');
    const { functionManager } = require('../functions/index.js');
    
    mockConfigManager = configManager;
    mockProviderManager = providerManager;
    mockFunctionManager = functionManager;
    
    arienCore = new ArienCore();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize providers with built-in functions', () => {
      expect(mockProviderManager.getAllProviders).toHaveBeenCalled();
      expect(mockFunctionManager.getAllFunctions).toHaveBeenCalled();
    });
  });

  describe('Provider Authentication', () => {
    it('should authenticate provider successfully', async () => {
      const authConfig: AuthConfig = {
        providerId: 'openai',
        apiKey: 'test-api-key'
      };

      const mockProvider = {
        setBuiltinFunctions: jest.fn(),
        validateConfig: jest.fn().mockResolvedValue(true)
      };

      mockProviderManager.createProvider.mockReturnValue(mockProvider);

      const result = await arienCore.authenticateProvider('openai', authConfig);

      expect(result).toBe(true);
      expect(mockProviderManager.createProvider).toHaveBeenCalledWith('openai', authConfig);
      expect(mockProvider.setBuiltinFunctions).toHaveBeenCalled();
      expect(mockProvider.validateConfig).toHaveBeenCalled();
      expect(mockConfigManager.setAuthConfig).toHaveBeenCalledWith('openai', authConfig);
    });

    it('should fail authentication for invalid config', async () => {
      const authConfig: AuthConfig = {
        providerId: 'openai',
        apiKey: 'invalid-key'
      };

      const mockProvider = {
        setBuiltinFunctions: jest.fn(),
        validateConfig: jest.fn().mockResolvedValue(false)
      };

      mockProviderManager.createProvider.mockReturnValue(mockProvider);

      const result = await arienCore.authenticateProvider('openai', authConfig);

      expect(result).toBe(false);
      expect(mockProviderManager.removeProvider).toHaveBeenCalledWith('openai');
      expect(mockConfigManager.setAuthConfig).not.toHaveBeenCalled();
    });

    it('should handle authentication errors', async () => {
      const authConfig: AuthConfig = {
        providerId: 'openai',
        apiKey: 'test-key'
      };

      mockProviderManager.createProvider.mockImplementation(() => {
        throw new Error('Provider creation failed');
      });

      const result = await arienCore.authenticateProvider('openai', authConfig);

      expect(result).toBe(false);
    });
  });

  describe('Chat Functionality', () => {
    it('should execute chat with current provider', async () => {
      const messages: ChatMessage[] = [
        global.testUtils.createMockChatMessage('user', 'Hello')
      ];

      const mockProvider = {
        chat: jest.fn().mockResolvedValue({
          content: 'Hello! How can I help you?',
          model: 'gpt-4o',
          provider: 'openai',
          usage: { inputTokens: 5, outputTokens: 10, totalTokens: 15 }
        })
      };

      mockConfigManager.getCurrentProvider.mockReturnValue('openai');
      mockConfigManager.getCurrentModel.mockReturnValue('gpt-4o');
      mockProviderManager.getProvider.mockReturnValue(mockProvider);

      const response = await arienCore.chat(messages);

      expect(response.content).toBe('Hello! How can I help you?');
      expect(mockProvider.chat).toHaveBeenCalledWith(
        messages,
        'gpt-4o',
        expect.objectContaining({
          systemPrompt: 'Default system prompt',
          functions: []
        })
      );
    });

    it('should create provider from auth config if not exists', async () => {
      const messages: ChatMessage[] = [
        global.testUtils.createMockChatMessage('user', 'Hello')
      ];

      const authConfig: AuthConfig = {
        providerId: 'openai',
        apiKey: 'test-key'
      };

      const mockProvider = {
        setBuiltinFunctions: jest.fn(),
        chat: jest.fn().mockResolvedValue({
          content: 'Response',
          model: 'gpt-4o',
          provider: 'openai'
        })
      };

      mockConfigManager.getCurrentProvider.mockReturnValue('openai');
      mockConfigManager.getCurrentModel.mockReturnValue('gpt-4o');
      mockProviderManager.getProvider.mockReturnValue(undefined);
      mockConfigManager.getAuthConfig.mockReturnValue(authConfig);
      mockProviderManager.createProvider.mockReturnValue(mockProvider);

      const response = await arienCore.chat(messages);

      expect(mockProviderManager.createProvider).toHaveBeenCalledWith('openai', authConfig);
      expect(mockProvider.setBuiltinFunctions).toHaveBeenCalled();
      expect(response.content).toBe('Response');
    });

    it('should throw error when no provider is selected', async () => {
      const messages: ChatMessage[] = [
        global.testUtils.createMockChatMessage('user', 'Hello')
      ];

      mockConfigManager.getCurrentProvider.mockReturnValue(undefined);
      mockConfigManager.getCurrentModel.mockReturnValue(undefined);

      await expect(arienCore.chat(messages))
        .rejects.toThrow('No provider or model selected');
    });

    it('should throw error when provider is not authenticated', async () => {
      const messages: ChatMessage[] = [
        global.testUtils.createMockChatMessage('user', 'Hello')
      ];

      mockConfigManager.getCurrentProvider.mockReturnValue('openai');
      mockConfigManager.getCurrentModel.mockReturnValue('gpt-4o');
      mockProviderManager.getProvider.mockReturnValue(undefined);
      mockConfigManager.getAuthConfig.mockReturnValue(undefined);

      await expect(arienCore.chat(messages))
        .rejects.toThrow('Provider openai not authenticated');
    });

    it('should use specified provider and model', async () => {
      const messages: ChatMessage[] = [
        global.testUtils.createMockChatMessage('user', 'Hello')
      ];

      const mockProvider = {
        chat: jest.fn().mockResolvedValue({
          content: 'Response',
          model: 'claude-3-5-sonnet',
          provider: 'anthropic'
        })
      };

      mockProviderManager.getProvider.mockReturnValue(mockProvider);

      await arienCore.chat(messages, 'anthropic', 'claude-3-5-sonnet');

      expect(mockProvider.chat).toHaveBeenCalledWith(
        messages,
        'claude-3-5-sonnet',
        expect.any(Object)
      );
    });
  });

  describe('Configuration Management', () => {
    it('should check if provider is authenticated', () => {
      mockConfigManager.isProviderAuthenticated.mockReturnValue(true);
      
      const result = arienCore.isProviderAuthenticated('openai');
      
      expect(result).toBe(true);
      expect(mockConfigManager.isProviderAuthenticated).toHaveBeenCalledWith('openai');
    });

    it('should get current provider and model', () => {
      mockConfigManager.getCurrentProvider.mockReturnValue('openai');
      mockConfigManager.getCurrentModel.mockReturnValue('gpt-4o');
      
      expect(arienCore.getCurrentProvider()).toBe('openai');
      expect(arienCore.getCurrentModel()).toBe('gpt-4o');
    });

    it('should set current provider and model', () => {
      arienCore.setCurrentProvider('anthropic', 'claude-3-5-sonnet');
      
      expect(mockConfigManager.setCurrentProvider).toHaveBeenCalledWith('anthropic', 'claude-3-5-sonnet');
    });

    it('should get and set theme', () => {
      mockConfigManager.getTheme.mockReturnValue('matrix');
      
      expect(arienCore.getTheme()).toBe('matrix');
      
      arienCore.setTheme('ocean');
      expect(mockConfigManager.setTheme).toHaveBeenCalledWith('ocean');
    });

    it('should get and set system prompt', () => {
      const prompt = 'Custom system prompt';
      mockConfigManager.getSystemPrompt.mockReturnValue(prompt);
      
      expect(arienCore.getSystemPrompt()).toBe(prompt);
      
      arienCore.setSystemPrompt('New prompt');
      expect(mockConfigManager.setSystemPrompt).toHaveBeenCalledWith('New prompt');
    });
  });

  describe('Chat History Management', () => {
    it('should add chat session', () => {
      const session = {
        id: 'test-session',
        title: 'Test Chat',
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        provider: 'openai',
        model: 'gpt-4o'
      };

      arienCore.addChatSession(session);
      
      expect(mockConfigManager.addChatSession).toHaveBeenCalledWith(session);
    });

    it('should get chat history', () => {
      const mockHistory = [{ id: 'session-1' }];
      mockConfigManager.getChatHistory.mockReturnValue(mockHistory);
      
      const history = arienCore.getChatHistory();
      
      expect(history).toEqual(mockHistory);
      expect(mockConfigManager.getChatHistory).toHaveBeenCalled();
    });

    it('should clear chat history', () => {
      arienCore.clearChatHistory();
      
      expect(mockConfigManager.clearChatHistory).toHaveBeenCalled();
    });
  });

  describe('Reset Functionality', () => {
    it('should reset all configuration and providers', () => {
      const mockProviders = new Map();
      mockProviderManager.getAllProviders.mockReturnValue(mockProviders);
      
      arienCore.reset();
      
      expect(mockConfigManager.reset).toHaveBeenCalled();
      expect(mockProviders.clear).toHaveBeenCalled();
    });
  });
});
