/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { AuthScreen } from './screens/AuthScreen.js';
import { ThemeSelectionScreen } from './screens/ThemeSelectionScreen.js';
import { ChatScreen } from './screens/ChatScreen.js';
import { LoadingScreen } from './screens/LoadingScreen.js';
import { ErrorBoundary } from './components/ErrorBoundary.js';
import { ThemeProvider, ArienCoreProvider } from './contexts/index.js';
import { useArienCore } from './hooks/useArienCore.js';
import { useTheme } from './hooks/useTheme.js';
import { CLIState } from '../core/types/index.js';

// Internal App component that uses hooks
const AppContent: React.FC = () => {
  const { currentProvider, currentModel, isProviderAuthenticated } = useArienCore();
  const { currentTheme } = useTheme();

  const [state, setState] = useState<CLIState>({
    isAuthenticated: false,
    currentScreen: 'auth',
    selectedTheme: currentTheme,
    isLoading: false
  });

  useEffect(() => {
    // Check if user is already authenticated
    if (currentProvider && isProviderAuthenticated(currentProvider)) {
      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        currentScreen: 'theme-selection',
        selectedProvider: currentProvider,
        selectedModel: currentModel
      }));
    }
  }, [currentProvider, currentModel, isProviderAuthenticated]);

  const { setCurrentProvider } = useArienCore();
  const { setTheme } = useTheme();

  const handleAuthentication = (providerId: string, modelId: string) => {
    setState(prev => ({
      ...prev,
      isAuthenticated: true,
      currentScreen: 'theme-selection',
      selectedProvider: providerId,
      selectedModel: modelId
    }));

    setCurrentProvider(providerId, modelId);
  };

  const handleThemeSelection = (themeId: string) => {
    setState(prev => ({
      ...prev,
      selectedTheme: themeId,
      currentScreen: 'chat'
    }));

    setTheme(themeId);
  };

  const handleError = (error: string) => {
    setState(prev => ({
      ...prev,
      error,
      isLoading: false
    }));
  };

  const setLoading = (loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading
    }));
  };

  const renderCurrentScreen = () => {
    if (state.isLoading) {
      return <LoadingScreen />;
    }

    switch (state.currentScreen) {
      case 'auth':
        return (
          <AuthScreen
            onAuthenticated={handleAuthentication}
            onError={handleError}
            setLoading={setLoading}
          />
        );
      
      case 'theme-selection':
        return (
          <ThemeSelectionScreen
            currentTheme={state.selectedTheme}
            onThemeSelected={handleThemeSelection}
            onError={handleError}
          />
        );
      
      case 'chat':
        return (
          <ChatScreen
            provider={state.selectedProvider!}
            model={state.selectedModel!}
            theme={state.selectedTheme}
            onError={handleError}
            setLoading={setLoading}
          />
        );
      
      default:
        return <AuthScreen onAuthenticated={handleAuthentication} onError={handleError} setLoading={setLoading} />;
    }
  };

  return (
    <ErrorBoundary>
      <Box flexDirection="column" minHeight={24}>
        {renderCurrentScreen()}
        {state.error && (
          <Box marginTop={1} paddingX={2}>
            <Box borderStyle="round" borderColor="red" paddingX={1}>
              <Text color="red">Error: {state.error}</Text>
            </Box>
          </Box>
        )}
      </Box>
    </ErrorBoundary>
  );
};

// Main App component with providers
export const App: React.FC = () => {
  return (
    <ArienCoreProvider>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </ArienCoreProvider>
  );
};
