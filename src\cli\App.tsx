/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { AuthScreen } from './screens/AuthScreen.js';
import { ThemeSelectionScreen } from './screens/ThemeSelectionScreen.js';
import { ChatScreen } from './screens/ChatScreen.js';
import { LoadingScreen } from './screens/LoadingScreen.js';
import { ErrorBoundary } from './components/ErrorBoundary.js';
import { arienCore } from '../core/index.js';
import { CLIState } from '../core/types/index.js';

export const App: React.FC = () => {
  const [state, setState] = useState<CLIState>({
    isAuthenticated: false,
    currentScreen: 'auth',
    selectedTheme: arienCore.getTheme(),
    isLoading: false
  });

  useEffect(() => {
    // Check if user is already authenticated
    const currentProvider = arienCore.getCurrentProvider();
    if (currentProvider && arienCore.isProviderAuthenticated(currentProvider)) {
      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        currentScreen: 'theme-selection',
        selectedProvider: currentProvider,
        selectedModel: arienCore.getCurrentModel()
      }));
    }
  }, []);

  const handleAuthentication = (providerId: string, modelId: string) => {
    setState(prev => ({
      ...prev,
      isAuthenticated: true,
      currentScreen: 'theme-selection',
      selectedProvider: providerId,
      selectedModel: modelId
    }));
    
    arienCore.setCurrentProvider(providerId, modelId);
  };

  const handleThemeSelection = (themeId: string) => {
    setState(prev => ({
      ...prev,
      selectedTheme: themeId,
      currentScreen: 'chat'
    }));
    
    arienCore.setTheme(themeId);
  };

  const handleError = (error: string) => {
    setState(prev => ({
      ...prev,
      error,
      isLoading: false
    }));
  };

  const setLoading = (loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading
    }));
  };

  const renderCurrentScreen = () => {
    if (state.isLoading) {
      return <LoadingScreen />;
    }

    switch (state.currentScreen) {
      case 'auth':
        return (
          <AuthScreen
            onAuthenticated={handleAuthentication}
            onError={handleError}
            setLoading={setLoading}
          />
        );
      
      case 'theme-selection':
        return (
          <ThemeSelectionScreen
            currentTheme={state.selectedTheme}
            onThemeSelected={handleThemeSelection}
            onError={handleError}
          />
        );
      
      case 'chat':
        return (
          <ChatScreen
            provider={state.selectedProvider!}
            model={state.selectedModel!}
            theme={state.selectedTheme}
            onError={handleError}
            setLoading={setLoading}
          />
        );
      
      default:
        return <AuthScreen onAuthenticated={handleAuthentication} onError={handleError} setLoading={setLoading} />;
    }
  };

  return (
    <ErrorBoundary>
      <Box flexDirection="column" minHeight={24}>
        {renderCurrentScreen()}
        {state.error && (
          <Box marginTop={1} paddingX={2}>
            <Box borderStyle="round" borderColor="red" paddingX={1}>
              <Text color="red">Error: {state.error}</Text>
            </Box>
          </Box>
        )}
      </Box>
    </ErrorBoundary>
  );
};
