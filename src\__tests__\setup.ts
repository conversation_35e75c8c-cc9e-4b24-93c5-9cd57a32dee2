/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

// Global test setup
import { jest } from '@jest/globals';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.ARIEN_TEST_MODE = 'true';

// Mock console methods to reduce noise in tests
const originalConsole = { ...console };

beforeEach(() => {
  // Reset console mocks before each test
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
  console.info = jest.fn();
});

afterEach(() => {
  // Restore console after each test
  Object.assign(console, originalConsole);
});

// Global test utilities
global.testUtils = {
  // Helper to create mock chat messages
  createMockChatMessage: (role: 'user' | 'assistant' | 'system', content: string) => ({
    id: `test-${Date.now()}-${Math.random()}`,
    role,
    content,
    timestamp: new Date(),
    metadata: {}
  }),

  // Helper to create mock provider config
  createMockProviderConfig: (providerId: string) => ({
    apiKey: `test-api-key-${providerId}`,
    baseUrl: `https://api.${providerId}.com`,
    organization: `test-org-${providerId}`
  }),

  // Helper to create mock theme
  createMockTheme: (id: string) => ({
    id,
    name: `Test Theme ${id}`,
    description: `Test theme for ${id}`,
    colors: {
      primary: '#00ff9f',
      secondary: '#ff0080',
      accent: '#00d4ff',
      background: '#0a0a0a',
      text: '#ffffff',
      success: '#00ff9f',
      warning: '#ffaa00',
      error: '#ff0080',
      muted: '#666666'
    },
    styles: {
      border: 'round',
      gradient: ['#00ff9f', '#00d4ff', '#ff0080']
    }
  }),

  // Helper to wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to create mock function calls
  createMockFunctionCall: (name: string, args: Record<string, any>) => ({
    id: `test-call-${Date.now()}`,
    name,
    arguments: args,
    result: `Mock result for ${name}`,
    error: undefined
  })
};

// Declare global types for TypeScript
declare global {
  var testUtils: {
    createMockChatMessage: (role: 'user' | 'assistant' | 'system', content: string) => any;
    createMockProviderConfig: (providerId: string) => any;
    createMockTheme: (id: string) => any;
    waitFor: (ms: number) => Promise<void>;
    createMockFunctionCall: (name: string, args: Record<string, any>) => any;
  };
}
