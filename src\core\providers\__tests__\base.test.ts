/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { BaseLLMProvider, LLMProviderConfig } from '../base.js';
import { ChatMessage, LLMResponse, BuiltinFunction, FunctionCall } from '../../types/index.js';

// Create a concrete implementation for testing
class TestProvider extends BaseLLMProvider {
  get providerId(): string {
    return 'test-provider';
  }

  get providerName(): string {
    return 'Test Provider';
  }

  async chat(
    messages: ChatMessage[],
    model: string,
    options?: any
  ): Promise<LLMResponse> {
    return {
      content: 'Test response',
      model,
      provider: this.providerId,
      usage: {
        inputTokens: 10,
        outputTokens: 20,
        totalTokens: 30
      }
    };
  }

  async validateConfig(): Promise<boolean> {
    return !!this.config.apiKey;
  }
}

describe('BaseLLMProvider', () => {
  let provider: TestProvider;
  let config: LLMProviderConfig;

  beforeEach(() => {
    config = {
      apiKey: 'test-api-key',
      baseUrl: 'https://api.test.com'
    };
    provider = new TestProvider(config);
  });

  describe('Constructor and Basic Properties', () => {
    it('should initialize with config', () => {
      expect((provider as any).config).toEqual(config);
      expect(provider.providerId).toBe('test-provider');
      expect(provider.providerName).toBe('Test Provider');
    });

    it('should initialize with empty builtin functions', () => {
      expect((provider as any).builtinFunctions).toEqual([]);
    });
  });

  describe('Builtin Functions Management', () => {
    it('should set builtin functions', () => {
      const mockFunctions: BuiltinFunction[] = [
        {
          name: 'test_function',
          description: 'Test function',
          parameters: {
            type: 'object',
            properties: {
              input: { type: 'string', description: 'Test input' }
            },
            required: ['input']
          },
          handler: async (args) => `Result: ${args.input}`
        }
      ];

      provider.setBuiltinFunctions(mockFunctions);
      expect((provider as any).builtinFunctions).toEqual(mockFunctions);
    });

    it('should execute function call successfully', async () => {
      const mockFunction: BuiltinFunction = {
        name: 'test_function',
        description: 'Test function',
        parameters: {
          type: 'object',
          properties: {
            input: { type: 'string', description: 'Test input' }
          },
          required: ['input']
        },
        handler: async (args) => `Result: ${args.input}`
      };

      provider.setBuiltinFunctions([mockFunction]);

      const functionCall: FunctionCall = {
        id: 'test-call',
        name: 'test_function',
        arguments: { input: 'test value' }
      };

      const result = await (provider as any).executeFunctionCall(functionCall);
      expect(result).toBe('Result: test value');
    });

    it('should throw error for unknown function', async () => {
      const functionCall: FunctionCall = {
        id: 'test-call',
        name: 'unknown_function',
        arguments: {}
      };

      await expect((provider as any).executeFunctionCall(functionCall))
        .rejects.toThrow('Unknown function: unknown_function');
    });

    it('should handle function execution errors', async () => {
      const mockFunction: BuiltinFunction = {
        name: 'error_function',
        description: 'Function that throws error',
        parameters: {
          type: 'object',
          properties: {},
          required: []
        },
        handler: async () => {
          throw new Error('Function execution failed');
        }
      };

      provider.setBuiltinFunctions([mockFunction]);

      const functionCall: FunctionCall = {
        id: 'test-call',
        name: 'error_function',
        arguments: {}
      };

      await expect((provider as any).executeFunctionCall(functionCall))
        .rejects.toThrow('Function execution failed: Function execution failed');
    });
  });

  describe('Message Formatting', () => {
    it('should format messages for provider without system prompt', () => {
      const messages: ChatMessage[] = [
        global.testUtils.createMockChatMessage('user', 'Hello'),
        global.testUtils.createMockChatMessage('assistant', 'Hi there!')
      ];

      const formatted = (provider as any).formatMessagesForProvider(messages);
      
      expect(formatted).toHaveLength(2);
      expect(formatted[0]).toEqual({
        role: 'user',
        content: 'Hello'
      });
      expect(formatted[1]).toEqual({
        role: 'assistant',
        content: 'Hi there!'
      });
    });

    it('should format messages for provider with system prompt', () => {
      const messages: ChatMessage[] = [
        global.testUtils.createMockChatMessage('user', 'Hello')
      ];

      const systemPrompt = 'You are a helpful assistant';
      const formatted = (provider as any).formatMessagesForProvider(messages, systemPrompt);
      
      expect(formatted).toHaveLength(2);
      expect(formatted[0]).toEqual({
        role: 'system',
        content: systemPrompt
      });
      expect(formatted[1]).toEqual({
        role: 'user',
        content: 'Hello'
      });
    });
  });

  describe('Function Formatting', () => {
    it('should format functions for provider', () => {
      const functions: BuiltinFunction[] = [
        {
          name: 'test_function',
          description: 'Test function',
          parameters: {
            type: 'object',
            properties: {
              input: { type: 'string', description: 'Test input' }
            },
            required: ['input']
          },
          handler: async () => 'test'
        }
      ];

      const formatted = (provider as any).formatFunctionsForProvider(functions);
      
      expect(formatted).toHaveLength(1);
      expect(formatted[0]).toEqual({
        name: 'test_function',
        description: 'Test function',
        parameters: {
          type: 'object',
          properties: {
            input: { type: 'string', description: 'Test input' }
          },
          required: ['input']
        }
      });
    });
  });

  describe('Utility Methods', () => {
    it('should create chat message', () => {
      const message = (provider as any).createChatMessage('user', 'Test content', { test: true });
      
      expect(message.role).toBe('user');
      expect(message.content).toBe('Test content');
      expect(message.metadata).toEqual({ test: true });
      expect(message.id).toBeDefined();
      expect(message.timestamp).toBeInstanceOf(Date);
    });

    it('should generate unique IDs', () => {
      const id1 = (provider as any).generateId();
      const id2 = (provider as any).generateId();
      
      expect(id1).toBeDefined();
      expect(id2).toBeDefined();
      expect(id1).not.toBe(id2);
    });

    it('should calculate tokens approximately', () => {
      const text = 'This is a test message with some content';
      const tokens = (provider as any).calculateTokens(text);
      
      // Should be roughly text.length / 4
      expect(tokens).toBeGreaterThan(0);
      expect(tokens).toBeLessThanOrEqual(Math.ceil(text.length / 4) + 1);
    });
  });

  describe('Abstract Methods Implementation', () => {
    it('should implement chat method', async () => {
      const messages: ChatMessage[] = [
        global.testUtils.createMockChatMessage('user', 'Hello')
      ];

      const response = await provider.chat(messages, 'test-model');
      
      expect(response.content).toBe('Test response');
      expect(response.model).toBe('test-model');
      expect(response.provider).toBe('test-provider');
      expect(response.usage).toEqual({
        inputTokens: 10,
        outputTokens: 20,
        totalTokens: 30
      });
    });

    it('should implement validateConfig method', async () => {
      const validProvider = new TestProvider({ apiKey: 'valid-key' });
      const invalidProvider = new TestProvider({});

      expect(await validProvider.validateConfig()).toBe(true);
      expect(await invalidProvider.validateConfig()).toBe(false);
    });
  });
});
