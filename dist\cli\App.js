import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { AuthScreen } from './screens/AuthScreen.js';
import { ThemeSelectionScreen } from './screens/ThemeSelectionScreen.js';
import { ChatScreen } from './screens/ChatScreen.js';
import { LoadingScreen } from './screens/LoadingScreen.js';
import { ErrorBoundary } from './components/ErrorBoundary.js';
import { arienCore } from '../core/index.js';
export const App = () => {
    const [state, setState] = useState({
        isAuthenticated: false,
        currentScreen: 'auth',
        selectedTheme: arienCore.getTheme(),
        isLoading: false
    });
    useEffect(() => {
        // Check if user is already authenticated
        const currentProvider = arienCore.getCurrentProvider();
        if (currentProvider && arienCore.isProviderAuthenticated(currentProvider)) {
            setState(prev => ({
                ...prev,
                isAuthenticated: true,
                currentScreen: 'theme-selection',
                selectedProvider: currentProvider,
                selectedModel: arienCore.getCurrentModel()
            }));
        }
    }, []);
    const handleAuthentication = (providerId, modelId) => {
        setState(prev => ({
            ...prev,
            isAuthenticated: true,
            currentScreen: 'theme-selection',
            selectedProvider: providerId,
            selectedModel: modelId
        }));
        arienCore.setCurrentProvider(providerId, modelId);
    };
    const handleThemeSelection = (themeId) => {
        setState(prev => ({
            ...prev,
            selectedTheme: themeId,
            currentScreen: 'chat'
        }));
        arienCore.setTheme(themeId);
    };
    const handleError = (error) => {
        setState(prev => ({
            ...prev,
            error,
            isLoading: false
        }));
    };
    const setLoading = (loading) => {
        setState(prev => ({
            ...prev,
            isLoading: loading
        }));
    };
    const renderCurrentScreen = () => {
        if (state.isLoading) {
            return _jsx(LoadingScreen, {});
        }
        switch (state.currentScreen) {
            case 'auth':
                return (_jsx(AuthScreen, { onAuthenticated: handleAuthentication, onError: handleError, setLoading: setLoading }));
            case 'theme-selection':
                return (_jsx(ThemeSelectionScreen, { currentTheme: state.selectedTheme, onThemeSelected: handleThemeSelection, onError: handleError }));
            case 'chat':
                return (_jsx(ChatScreen, { provider: state.selectedProvider, model: state.selectedModel, theme: state.selectedTheme, onError: handleError, setLoading: setLoading }));
            default:
                return _jsx(AuthScreen, { onAuthenticated: handleAuthentication, onError: handleError, setLoading: setLoading });
        }
    };
    return (_jsx(ErrorBoundary, { children: _jsxs(Box, { flexDirection: "column", minHeight: 24, children: [renderCurrentScreen(), state.error && (_jsx(Box, { marginTop: 1, paddingX: 2, children: _jsx(Box, { borderStyle: "round", borderColor: "red", paddingX: 1, children: _jsxs(Text, { color: "red", children: ["Error: ", state.error] }) }) }))] }) }));
};
//# sourceMappingURL=App.js.map