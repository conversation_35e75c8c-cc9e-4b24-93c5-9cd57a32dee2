/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { Auth<PERSON>onfig, LLMProvider, Theme } from '../../core/types/index.js';

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * Validate API key format
 */
export const validateApiKey = (apiKey: string, providerId: string): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!apiKey || typeof apiKey !== 'string') {
    errors.push('API key is required');
    return { isValid: false, errors, warnings };
  }

  if (apiKey.trim().length === 0) {
    errors.push('API key cannot be empty');
    return { isValid: false, errors, warnings };
  }

  // Provider-specific validation
  switch (providerId) {
    case 'openai':
      if (!apiKey.startsWith('sk-')) {
        errors.push('OpenAI API key should start with "sk-"');
      }
      if (apiKey.length < 20) {
        errors.push('OpenAI API key appears to be too short');
      }
      break;

    case 'anthropic':
      if (!apiKey.startsWith('sk-ant-')) {
        errors.push('Anthropic API key should start with "sk-ant-"');
      }
      break;

    case 'google':
      if (apiKey.length < 20) {
        warnings.push('Google API key appears to be short, please verify it is correct');
      }
      break;

    case 'deepseek':
      if (!apiKey.startsWith('sk-')) {
        warnings.push('DeepSeek API key typically starts with "sk-"');
      }
      break;
  }

  // Check for common issues
  if (apiKey.includes(' ')) {
    errors.push('API key should not contain spaces');
  }

  if (apiKey !== apiKey.trim()) {
    errors.push('API key should not have leading or trailing whitespace');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate authentication configuration
 */
export const validateAuthConfig = (config: AuthConfig): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!config.providerId) {
    errors.push('Provider ID is required');
  }

  // Check if at least one authentication method is provided
  const hasApiKey = config.apiKey && config.apiKey.trim().length > 0;
  const hasOAuth = config.accessToken && config.accessToken.trim().length > 0;

  if (!hasApiKey && !hasOAuth) {
    errors.push('Either API key or OAuth tokens are required');
  }

  // Validate API key if provided
  if (hasApiKey) {
    const apiKeyValidation = validateApiKey(config.apiKey!, config.providerId);
    errors.push(...apiKeyValidation.errors);
    if (apiKeyValidation.warnings) {
      warnings.push(...apiKeyValidation.warnings);
    }
  }

  // Validate OAuth tokens if provided
  if (hasOAuth) {
    if (config.expiresAt && config.expiresAt < Date.now()) {
      errors.push('OAuth token has expired');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate chat message content
 */
export const validateMessageContent = (content: string): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!content || typeof content !== 'string') {
    errors.push('Message content is required');
    return { isValid: false, errors, warnings };
  }

  if (content.trim().length === 0) {
    errors.push('Message content cannot be empty');
    return { isValid: false, errors, warnings };
  }

  // Check message length
  if (content.length > 100000) {
    errors.push('Message content is too long (max 100,000 characters)');
  } else if (content.length > 50000) {
    warnings.push('Message content is very long and may be truncated');
  }

  // Check for potentially problematic content
  if (content.includes('\0')) {
    errors.push('Message content contains null characters');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate theme configuration
 */
export const validateTheme = (theme: Theme): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!theme.id || typeof theme.id !== 'string') {
    errors.push('Theme ID is required');
  }

  if (!theme.name || typeof theme.name !== 'string') {
    errors.push('Theme name is required');
  }

  if (!theme.colors || typeof theme.colors !== 'object') {
    errors.push('Theme colors are required');
  } else {
    // Validate required color properties
    const requiredColors = ['primary', 'secondary', 'accent', 'background', 'text', 'success', 'warning', 'error', 'muted'];
    for (const colorKey of requiredColors) {
      if (!theme.colors[colorKey as keyof typeof theme.colors]) {
        errors.push(`Theme color '${colorKey}' is required`);
      }
    }

    // Validate color format (basic hex color validation)
    for (const [key, color] of Object.entries(theme.colors)) {
      if (typeof color === 'string' && !isValidHexColor(color)) {
        warnings.push(`Theme color '${key}' may not be a valid hex color: ${color}`);
      }
    }
  }

  if (!theme.styles || typeof theme.styles !== 'object') {
    errors.push('Theme styles are required');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate provider configuration
 */
export const validateProvider = (provider: LLMProvider): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!provider.id || typeof provider.id !== 'string') {
    errors.push('Provider ID is required');
  }

  if (!provider.name || typeof provider.name !== 'string') {
    errors.push('Provider name is required');
  }

  if (!Array.isArray(provider.models) || provider.models.length === 0) {
    errors.push('Provider must have at least one model');
  } else {
    // Validate each model
    provider.models.forEach((model, index) => {
      if (!model.id || typeof model.id !== 'string') {
        errors.push(`Model ${index + 1} ID is required`);
      }
      if (!model.name || typeof model.name !== 'string') {
        errors.push(`Model ${index + 1} name is required`);
      }
      if (typeof model.maxTokens !== 'number' || model.maxTokens <= 0) {
        errors.push(`Model ${index + 1} must have a valid maxTokens value`);
      }
    });
  }

  if (!Array.isArray(provider.configFields)) {
    errors.push('Provider config fields must be an array');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate URL format
 */
export const validateUrl = (url: string): ValidationResult => {
  const errors: string[] = [];

  if (!url || typeof url !== 'string') {
    errors.push('URL is required');
    return { isValid: false, errors };
  }

  try {
    new URL(url);
  } catch {
    errors.push('Invalid URL format');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): ValidationResult => {
  const errors: string[] = [];

  if (!email || typeof email !== 'string') {
    errors.push('Email is required');
    return { isValid: false, errors };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    errors.push('Invalid email format');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Helper function to validate hex color format
 */
const isValidHexColor = (color: string): boolean => {
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return hexColorRegex.test(color);
};

/**
 * Validate function call arguments
 */
export const validateFunctionArgs = (
  args: Record<string, any>,
  parameters: {
    type: string;
    properties: Record<string, any>;
    required: string[];
  }
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check required parameters
  for (const requiredParam of parameters.required) {
    if (!(requiredParam in args)) {
      errors.push(`Missing required parameter: ${requiredParam}`);
    }
  }

  // Validate parameter types
  for (const [key, value] of Object.entries(args)) {
    const paramSchema = parameters.properties[key];
    if (paramSchema) {
      const expectedType = paramSchema.type;
      const actualType = typeof value;

      switch (expectedType) {
        case 'string':
          if (actualType !== 'string') {
            errors.push(`Parameter '${key}' must be a string`);
          }
          break;
        case 'number':
          if (actualType !== 'number') {
            errors.push(`Parameter '${key}' must be a number`);
          }
          break;
        case 'boolean':
          if (actualType !== 'boolean') {
            errors.push(`Parameter '${key}' must be a boolean`);
          }
          break;
        case 'array':
          if (!Array.isArray(value)) {
            errors.push(`Parameter '${key}' must be an array`);
          }
          break;
        case 'object':
          if (actualType !== 'object' || Array.isArray(value) || value === null) {
            errors.push(`Parameter '${key}' must be an object`);
          }
          break;
      }
    } else {
      warnings.push(`Unknown parameter: ${key}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};
