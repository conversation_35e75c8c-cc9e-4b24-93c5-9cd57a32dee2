/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { createContext, useContext, ReactNode } from 'react';
import { useArienCore, UseArienCoreReturn } from '../hooks/useArienCore.js';

const ArienCoreContext = createContext<UseArienCoreReturn | undefined>(undefined);

export interface ArienCoreProviderProps {
  children: ReactNode;
}

/**
 * ArienCore context provider component
 */
export const ArienCoreProvider: React.FC<ArienCoreProviderProps> = ({ children }) => {
  const arienCoreState = useArienCore();

  return (
    <ArienCoreContext.Provider value={arienCoreState}>
      {children}
    </ArienCoreContext.Provider>
  );
};

/**
 * Hook to use ArienCore context
 */
export const useArienCoreContext = (): UseArienCoreReturn => {
  const context = useContext(ArienCoreContext);
  
  if (context === undefined) {
    throw new Error('useArienCoreContext must be used within an ArienCoreProvider');
  }
  
  return context;
};
