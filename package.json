{"name": "arien-ai-cli", "version": "1.0.0", "description": "Modern AI-powered CLI terminal tool for interacting with Large Language Models", "main": "dist/index.js", "type": "module", "bin": {"arien": "dist/index.js"}, "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "keywords": ["ai", "cli", "terminal", "llm", "chat", "typescript", "react"], "author": "Arien LLC", "license": "MIT", "engines": {"node": ">=20.0.0"}, "dependencies": {"react": "^18.2.0", "ink": "^4.4.1", "ink-text-input": "^5.0.1", "ink-select-input": "^5.0.0", "ink-spinner": "^5.0.0", "ink-box": "^2.0.0", "ink-gradient": "^2.0.0", "ink-big-text": "^2.0.0", "openai": "^4.67.3", "google-auth-library": "^9.14.1", "@google/generative-ai": "^0.21.0", "@anthropic-ai/sdk": "^0.27.3", "axios": "^1.7.7", "chalk": "^5.3.0", "commander": "^12.1.0", "conf": "^13.0.1", "inquirer": "^12.0.0", "ora": "^8.1.0", "figlet": "^1.7.0", "gradient-string": "^2.0.2", "nanoid": "^5.0.7", "zod": "^3.23.8", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^22.7.4", "@types/react": "^18.2.79", "@types/figlet": "^1.5.8", "@types/gradient-string": "^1.1.6", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.0", "ink-testing-library": "^3.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.2", "rimraf": "^6.0.1", "tsx": "^4.19.1", "typescript": "^5.6.3"}}