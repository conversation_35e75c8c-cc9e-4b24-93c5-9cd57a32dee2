/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { ConfigManager } from '../index.js';
import { AuthConfig } from '../../types/index.js';

// Mock the Conf module
jest.mock('conf', () => {
  return {
    default: jest.fn().mockImplementation(() => ({
      get: jest.fn(),
      set: jest.fn(),
      clear: jest.fn(),
      path: '/mock/config/path'
    }))
  };
});

describe('ConfigManager', () => {
  let configManager: ConfigManager;
  let mockConf: any;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create a new instance for each test
    configManager = new ConfigManager();
    mockConf = (configManager as any).config;
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Authentication Configuration', () => {
    it('should set and get auth config correctly', () => {
      const authConfig: AuthConfig = {
        providerId: 'openai',
        apiKey: 'test-api-key',
        additionalConfig: { organization: 'test-org' }
      };

      mockConf.get.mockReturnValue({});
      
      configManager.setAuthConfig('openai', authConfig);
      
      expect(mockConf.set).toHaveBeenCalledWith('authConfigs', {
        openai: authConfig
      });
    });

    it('should check if provider is authenticated with API key', () => {
      const authConfig: AuthConfig = {
        providerId: 'openai',
        apiKey: 'test-api-key'
      };

      mockConf.get.mockReturnValue({ openai: authConfig });
      
      const isAuthenticated = configManager.isProviderAuthenticated('openai');
      
      expect(isAuthenticated).toBe(true);
    });

    it('should check if provider is authenticated with OAuth tokens', () => {
      const authConfig: AuthConfig = {
        providerId: 'google',
        accessToken: 'test-access-token',
        expiresAt: Date.now() + 3600000 // 1 hour from now
      };

      mockConf.get.mockReturnValue({ google: authConfig });
      
      const isAuthenticated = configManager.isProviderAuthenticated('google');
      
      expect(isAuthenticated).toBe(true);
    });

    it('should return false for expired OAuth tokens', () => {
      const authConfig: AuthConfig = {
        providerId: 'google',
        accessToken: 'test-access-token',
        expiresAt: Date.now() - 3600000 // 1 hour ago
      };

      mockConf.get.mockReturnValue({ google: authConfig });
      
      const isAuthenticated = configManager.isProviderAuthenticated('google');
      
      expect(isAuthenticated).toBe(false);
    });

    it('should return false for non-existent provider', () => {
      mockConf.get.mockReturnValue({});
      
      const isAuthenticated = configManager.isProviderAuthenticated('nonexistent');
      
      expect(isAuthenticated).toBe(false);
    });

    it('should remove auth config correctly', () => {
      const initialConfigs = {
        openai: { providerId: 'openai', apiKey: 'test-key' },
        google: { providerId: 'google', apiKey: 'test-key-2' }
      };

      mockConf.get.mockReturnValue(initialConfigs);
      
      configManager.removeAuthConfig('openai');
      
      expect(mockConf.set).toHaveBeenCalledWith('authConfigs', {
        google: { providerId: 'google', apiKey: 'test-key-2' }
      });
    });
  });

  describe('Provider and Model Management', () => {
    it('should set and get current provider', () => {
      configManager.setCurrentProvider('openai', 'gpt-4o');
      
      expect(mockConf.set).toHaveBeenCalledWith('currentProvider', 'openai');
      expect(mockConf.set).toHaveBeenCalledWith('currentModel', 'gpt-4o');
    });

    it('should set provider without model', () => {
      configManager.setCurrentProvider('anthropic');
      
      expect(mockConf.set).toHaveBeenCalledWith('currentProvider', 'anthropic');
      expect(mockConf.set).not.toHaveBeenCalledWith('currentModel', expect.anything());
    });

    it('should get current provider and model', () => {
      mockConf.get.mockImplementation((key: string) => {
        if (key === 'currentProvider') return 'openai';
        if (key === 'currentModel') return 'gpt-4o';
        return undefined;
      });

      expect(configManager.getCurrentProvider()).toBe('openai');
      expect(configManager.getCurrentModel()).toBe('gpt-4o');
    });
  });

  describe('Theme Management', () => {
    it('should set and get theme', () => {
      mockConf.get.mockReturnValue('cyberpunk');
      
      configManager.setTheme('matrix');
      expect(mockConf.set).toHaveBeenCalledWith('theme', 'matrix');
      
      const theme = configManager.getTheme();
      expect(theme).toBe('cyberpunk');
    });
  });

  describe('System Prompt Management', () => {
    it('should set and get system prompt', () => {
      const testPrompt = 'Test system prompt';
      mockConf.get.mockReturnValue(testPrompt);
      
      configManager.setSystemPrompt(testPrompt);
      expect(mockConf.set).toHaveBeenCalledWith('systemPrompt', testPrompt);
      
      const prompt = configManager.getSystemPrompt();
      expect(prompt).toBe(testPrompt);
    });
  });

  describe('Chat History Management', () => {
    it('should add chat session to history', () => {
      const mockHistory = [];
      const mockPreferences = { maxHistoryLength: 100 };
      
      mockConf.get.mockImplementation((key: string) => {
        if (key === 'chatHistory') return mockHistory;
        if (key === 'preferences') return mockPreferences;
        return undefined;
      });

      const session = {
        id: 'test-session',
        title: 'Test Chat',
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        provider: 'openai',
        model: 'gpt-4o'
      };

      configManager.addChatSession(session);
      
      expect(mockConf.set).toHaveBeenCalledWith('chatHistory', [session]);
    });

    it('should limit chat history length', () => {
      const mockHistory = new Array(100).fill(null).map((_, i) => ({ id: `session-${i}` }));
      const mockPreferences = { maxHistoryLength: 50 };
      
      mockConf.get.mockImplementation((key: string) => {
        if (key === 'chatHistory') return mockHistory;
        if (key === 'preferences') return mockPreferences;
        return undefined;
      });

      const newSession = { id: 'new-session' };
      configManager.addChatSession(newSession);
      
      // Should call set with array of length 50 (maxHistoryLength)
      expect(mockConf.set).toHaveBeenCalledWith('chatHistory', 
        expect.arrayContaining([newSession])
      );
      
      const setCall = (mockConf.set as jest.Mock).mock.calls.find(call => call[0] === 'chatHistory');
      expect(setCall[1]).toHaveLength(50);
    });

    it('should clear chat history', () => {
      configManager.clearChatHistory();
      expect(mockConf.set).toHaveBeenCalledWith('chatHistory', []);
    });

    it('should get chat history', () => {
      const mockHistory = [{ id: 'session-1' }, { id: 'session-2' }];
      mockConf.get.mockReturnValue(mockHistory);
      
      const history = configManager.getChatHistory();
      expect(history).toEqual(mockHistory);
    });
  });

  describe('Configuration Reset', () => {
    it('should reset all configuration', () => {
      configManager.reset();
      expect(mockConf.clear).toHaveBeenCalled();
    });
  });

  describe('Configuration Path', () => {
    it('should return config path', () => {
      const path = configManager.getConfigPath();
      expect(path).toBe('/mock/config/path');
    });
  });
});
