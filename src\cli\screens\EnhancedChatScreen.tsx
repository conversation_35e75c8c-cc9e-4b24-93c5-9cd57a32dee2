/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect, useRef } from 'react';
import { Box, Text, useInput } from 'ink';
import TextInput from 'ink-text-input';
import { Header } from '../components/Header.js';
import { ChatMessage } from '../components/ChatMessage.js';
import { StatusBar } from '../components/StatusBar.js';
import { useChat } from '../hooks/useChat.js';
import { useThemeContext } from '../contexts/ThemeContext.js';
import { useArienCoreContext } from '../contexts/ArienCoreContext.js';
import { executeCommand, getCommandSuggestions } from '../utils/commands.js';
import { validateMessageContent } from '../utils/validation.js';
import { formatProviderName, formatModelName } from '../utils/formatting.js';
import { nanoid } from 'nanoid';

interface EnhancedChatScreenProps {
  provider: string;
  model: string;
  onError: (error: string) => void;
  setLoading: (loading: boolean) => void;
}

interface ChatState {
  inputValue: string;
  showHelp: boolean;
  showCommands: boolean;
  commandSuggestions: string[];
}

export const EnhancedChatScreen: React.FC<EnhancedChatScreenProps> = ({
  provider,
  model,
  onError,
  setLoading
}) => {
  const { currentTheme } = useThemeContext();
  const { systemPrompt } = useArienCoreContext();
  const {
    messages,
    isTyping,
    error,
    sendMessage,
    addMessage,
    clearMessages,
    createSystemMessage,
    saveSession,
    clearError,
    messageCount,
    tokenUsage
  } = useChat();

  const [state, setState] = useState<ChatState>({
    inputValue: '',
    showHelp: false,
    showCommands: false,
    commandSuggestions: []
  });

  const messagesEndRef = useRef<any>();

  useEffect(() => {
    // Add welcome message
    const welcomeMessage = createSystemMessage(
      `Welcome to Arien AI! 🚀\n\nYou're connected to ${formatProviderName(provider)} using the ${formatModelName(model)} model.\nType your message below or use /help for available commands.\n\nI have access to various built-in functions including:\n• System commands and file operations\n• Web search and URL fetching\n• Mathematical calculations\n• Text analysis and encoding\n• And much more!\n\nHow can I assist you today?`
    );

    addMessage(welcomeMessage);
  }, [provider, model, createSystemMessage, addMessage]);

  useEffect(() => {
    // Handle chat errors
    if (error) {
      onError(error);
      clearError();
    }
  }, [error, onError, clearError]);

  useEffect(() => {
    // Update loading state
    setLoading(isTyping);
  }, [isTyping, setLoading]);

  useEffect(() => {
    // Update command suggestions when input changes
    if (state.inputValue.startsWith('/')) {
      const suggestions = getCommandSuggestions(state.inputValue);
      setState(prev => ({
        ...prev,
        commandSuggestions: suggestions,
        showCommands: suggestions.length > 0
      }));
    } else {
      setState(prev => ({
        ...prev,
        commandSuggestions: [],
        showCommands: false
      }));
    }
  }, [state.inputValue]);

  const handleSendMessage = async () => {
    if (!state.inputValue.trim() || isTyping) return;

    const input = state.inputValue.trim();

    // Clear input immediately
    setState(prev => ({ ...prev, inputValue: '' }));

    // Check if it's a command
    if (input.startsWith('/')) {
      try {
        const result = await executeCommand(input);
        
        // Add command result as system message
        const commandMessage = createSystemMessage(result.message);
        addMessage(commandMessage);

        // Handle special command actions
        if (result.data) {
          switch (result.data.action) {
            case 'clear_messages':
              clearMessages();
              break;
            case 'save_session':
              saveSession(result.data.title);
              break;
            case 'exit':
              process.exit(0);
              break;
            case 'change_theme':
              // Theme change is handled by the command itself
              break;
          }
        }
      } catch (error) {
        const errorMessage = createSystemMessage(
          `Command error: ${error instanceof Error ? error.message : String(error)}`
        );
        addMessage(errorMessage);
      }
      return;
    }

    // Validate message content
    const validation = validateMessageContent(input);
    if (!validation.isValid) {
      const errorMessage = createSystemMessage(
        `Message validation failed: ${validation.errors.join(', ')}`
      );
      addMessage(errorMessage);
      return;
    }

    // Send regular chat message
    try {
      await sendMessage(input, {
        enableFunctions: true,
        systemPrompt
      });
    } catch (error) {
      // Error handling is done in the useChat hook
      console.error('Chat error:', error);
    }
  };

  const handleKeyPress = (input: string, key: any) => {
    if (key.return && !key.shift) {
      handleSendMessage();
    } else if (key.escape) {
      setState(prev => ({ 
        ...prev, 
        showHelp: false, 
        showCommands: false,
        commandSuggestions: []
      }));
    } else if (key.ctrl && input === 'c') {
      process.exit(0);
    } else if (key.tab && state.commandSuggestions.length > 0) {
      // Auto-complete first suggestion
      setState(prev => ({
        ...prev,
        inputValue: state.commandSuggestions[0] + ' ',
        showCommands: false,
        commandSuggestions: []
      }));
    }
  };

  useInput(handleKeyPress);

  return (
    <Box flexDirection="column" height="100%">
      {/* Header */}
      <Header 
        title={`${formatProviderName(provider)} • ${formatModelName(model)}`}
        subtitle="AI-Powered Terminal Chat"
        theme={currentTheme}
        showLogo={false}
      />

      {/* Help panel */}
      {state.showHelp && (
        <Box 
          borderStyle="round" 
          borderColor={currentTheme.colors.accent}
          margin={1}
          padding={1}
        >
          <Box flexDirection="column">
            <Text color={currentTheme.colors.accent} bold>
              📚 Available Commands:
            </Text>
            <Text color={currentTheme.colors.text}>
              /help - Toggle this help panel
            </Text>
            <Text color={currentTheme.colors.text}>
              /clear - Clear chat history
            </Text>
            <Text color={currentTheme.colors.text}>
              /theme [name] - Change theme
            </Text>
            <Text color={currentTheme.colors.text}>
              /status - Show current status
            </Text>
            <Text color={currentTheme.colors.text}>
              /save [title] - Save chat session
            </Text>
            <Text color={currentTheme.colors.muted}>
              Press Esc to close, Tab for autocomplete, Ctrl+C to exit
            </Text>
          </Box>
        </Box>
      )}

      {/* Command suggestions */}
      {state.showCommands && state.commandSuggestions.length > 0 && (
        <Box 
          borderStyle="single" 
          borderColor={currentTheme.colors.muted}
          margin={1}
          padding={1}
        >
          <Box flexDirection="column">
            <Text color={currentTheme.colors.muted}>
              💡 Command suggestions (press Tab to complete):
            </Text>
            {state.commandSuggestions.map((suggestion, index) => (
              <Text key={index} color={currentTheme.colors.accent}>
                {suggestion}
              </Text>
            ))}
          </Box>
        </Box>
      )}

      {/* Chat messages */}
      <Box flexDirection="column" flexGrow={1} padding={1}>
        {messages.map((message) => (
          <ChatMessage 
            key={message.id}
            message={message}
            theme={currentTheme}
          />
        ))}
        
        {isTyping && (
          <Box marginTop={1}>
            <Text color={currentTheme.colors.muted}>
              🤖 AI is thinking...
            </Text>
          </Box>
        )}
        
        <div ref={messagesEndRef} />
      </Box>

      {/* Input area */}
      <Box 
        borderStyle="round" 
        borderColor={currentTheme.colors.primary}
        margin={1}
        padding={1}
      >
        <Box alignItems="center" width="100%">
          <Text color={currentTheme.colors.accent}>
            💬
          </Text>
          <Box flexGrow={1}>
            <TextInput
              value={state.inputValue}
              onChange={(value) => setState(prev => ({ ...prev, inputValue: value }))}
              placeholder="Type your message... (Enter to send, /help for commands)"
            />
          </Box>
        </Box>
      </Box>

      {/* Enhanced Status bar */}
      <StatusBar 
        provider={provider}
        model={model}
        theme={currentTheme}
        messageCount={messageCount}
        isTyping={isTyping}
      />
      
      {/* Token usage display */}
      {tokenUsage.totalTokens > 0 && (
        <Box paddingX={2}>
          <Text color={currentTheme.colors.muted}>
            📊 Tokens: {tokenUsage.totalInputTokens} in + {tokenUsage.totalOutputTokens} out = {tokenUsage.totalTokens} total
          </Text>
        </Box>
      )}
    </Box>
  );
};
