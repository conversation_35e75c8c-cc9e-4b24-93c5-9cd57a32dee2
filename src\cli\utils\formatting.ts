/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { ChatMessage, FunctionCall } from '../../core/types/index.js';

/**
 * Format timestamp for display in chat messages
 */
export const formatTimestamp = (timestamp: Date, format: 'short' | 'long' = 'short'): string => {
  const options: Intl.DateTimeFormatOptions = format === 'short' 
    ? { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit' 
      }
    : { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit',
        day: '2-digit',
        month: '2-digit'
      };

  return timestamp.toLocaleTimeString('en-US', options);
};

/**
 * Format token usage for display
 */
export const formatTokenUsage = (tokens: {
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
}): string => {
  return `${tokens.inputTokens} in + ${tokens.outputTokens} out = ${tokens.totalTokens} total`;
};

/**
 * Format function call arguments for display
 */
export const formatFunctionArguments = (args: Record<string, any>): string => {
  const argStrings = Object.entries(args).map(([key, value]) => {
    if (typeof value === 'string') {
      return `${key}: "${value}"`;
    } else if (typeof value === 'object') {
      return `${key}: ${JSON.stringify(value)}`;
    } else {
      return `${key}: ${value}`;
    }
  });
  
  return argStrings.join(', ');
};

/**
 * Format function call result for display
 */
export const formatFunctionResult = (result: any): string => {
  if (typeof result === 'string') {
    return result;
  } else if (typeof result === 'object') {
    return JSON.stringify(result, null, 2);
  } else {
    return String(result);
  }
};

/**
 * Truncate text to specified length with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength - 3) + '...';
};

/**
 * Format message content for display (handle line breaks, etc.)
 */
export const formatMessageContent = (content: string): string => {
  // Replace multiple consecutive newlines with double newlines
  return content.replace(/\n{3,}/g, '\n\n');
};

/**
 * Get role display name and icon
 */
export const getRoleDisplay = (role: ChatMessage['role']): { name: string; icon: string } => {
  switch (role) {
    case 'user':
      return { name: 'You', icon: '👤' };
    case 'assistant':
      return { name: 'AI', icon: '🤖' };
    case 'system':
      return { name: 'System', icon: '⚙️' };
    default:
      return { name: 'Unknown', icon: '❓' };
  }
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
};

/**
 * Format duration for display
 */
export const formatDuration = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

/**
 * Format provider name for display
 */
export const formatProviderName = (providerId: string): string => {
  const providerNames: Record<string, string> = {
    'openai': 'OpenAI',
    'anthropic': 'Anthropic',
    'google': 'Google Gemini',
    'deepseek': 'DeepSeek'
  };

  return providerNames[providerId] || providerId.toUpperCase();
};

/**
 * Format model name for display
 */
export const formatModelName = (modelId: string): string => {
  // Remove common prefixes and format nicely
  return modelId
    .replace(/^(gpt-|claude-|gemini-|deepseek-)/, '')
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
};

/**
 * Generate a color based on a string (for consistent coloring)
 */
export const generateColorFromString = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  const hue = Math.abs(hash) % 360;
  return `hsl(${hue}, 70%, 50%)`;
};

/**
 * Format error message for display
 */
export const formatErrorMessage = (error: string | Error): string => {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
};

/**
 * Check if text contains code blocks
 */
export const hasCodeBlocks = (text: string): boolean => {
  return /```[\s\S]*?```/.test(text) || /`[^`\n]+`/.test(text);
};

/**
 * Extract code blocks from text
 */
export const extractCodeBlocks = (text: string): Array<{ language: string; code: string }> => {
  const codeBlocks: Array<{ language: string; code: string }> = [];
  const regex = /```(\w+)?\n?([\s\S]*?)```/g;
  let match;

  while ((match = regex.exec(text)) !== null) {
    codeBlocks.push({
      language: match[1] || 'text',
      code: match[2].trim()
    });
  }

  return codeBlocks;
};
