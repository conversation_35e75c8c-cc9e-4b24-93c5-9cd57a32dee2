/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { arienCore } from '../../core/index.js';
import { THEMES } from '../../core/config/themes.js';
import { PROVIDERS } from '../../core/config/providers.js';

export interface CommandResult {
  success: boolean;
  message: string;
  data?: any;
}

export interface Command {
  name: string;
  description: string;
  usage: string;
  aliases?: string[];
  handler: (args: string[]) => Promise<CommandResult> | CommandResult;
}

/**
 * Available CLI commands
 */
export const commands: Record<string, Command> = {
  help: {
    name: 'help',
    description: 'Show available commands',
    usage: '/help [command]',
    aliases: ['h', '?'],
    handler: (args: string[]) => {
      if (args.length > 0) {
        const commandName = args[0].toLowerCase();
        const command = commands[commandName] || Object.values(commands).find(cmd => 
          cmd.aliases?.includes(commandName)
        );
        
        if (command) {
          return {
            success: true,
            message: `📚 ${command.name.toUpperCase()}\n\n${command.description}\n\nUsage: ${command.usage}${
              command.aliases ? `\nAliases: ${command.aliases.join(', ')}` : ''
            }`
          };
        } else {
          return {
            success: false,
            message: `❌ Unknown command: ${commandName}`
          };
        }
      }

      const helpText = [
        '📚 Available Commands:',
        '',
        ...Object.values(commands).map(cmd => 
          `${cmd.usage.padEnd(25)} - ${cmd.description}`
        ),
        '',
        'Use /help [command] for detailed information about a specific command.',
        'Press Esc to close help, Ctrl+C to exit.'
      ].join('\n');

      return {
        success: true,
        message: helpText
      };
    }
  },

  clear: {
    name: 'clear',
    description: 'Clear chat history',
    usage: '/clear',
    aliases: ['cls'],
    handler: () => {
      return {
        success: true,
        message: '🧹 Chat history cleared',
        data: { action: 'clear_messages' }
      };
    }
  },

  theme: {
    name: 'theme',
    description: 'Change or list themes',
    usage: '/theme [theme-id]',
    aliases: ['t'],
    handler: (args: string[]) => {
      if (args.length === 0) {
        const currentTheme = arienCore.getTheme();
        const themeList = THEMES.map(theme => 
          `${theme.id === currentTheme ? '→' : ' '} ${theme.id.padEnd(12)} - ${theme.name}`
        ).join('\n');

        return {
          success: true,
          message: `🎨 Available Themes:\n\n${themeList}\n\nUse /theme [id] to switch themes.`
        };
      }

      const themeId = args[0].toLowerCase();
      const theme = THEMES.find(t => t.id === themeId || t.name.toLowerCase() === themeId);

      if (theme) {
        arienCore.setTheme(theme.id);
        return {
          success: true,
          message: `🎨 Theme changed to: ${theme.name}`,
          data: { action: 'change_theme', themeId: theme.id }
        };
      } else {
        return {
          success: false,
          message: `❌ Theme not found: ${themeId}`
        };
      }
    }
  },

  status: {
    name: 'status',
    description: 'Show current status and configuration',
    usage: '/status',
    aliases: ['info', 'i'],
    handler: () => {
      const provider = arienCore.getCurrentProvider();
      const model = arienCore.getCurrentModel();
      const theme = arienCore.getTheme();
      const history = arienCore.getChatHistory();

      const statusInfo = [
        '📊 Current Status:',
        '',
        `🔗 Provider: ${provider || 'None selected'}`,
        `🤖 Model: ${model || 'None selected'}`,
        `🎨 Theme: ${theme}`,
        `💬 Chat Sessions: ${history.length}`,
        `⚙️  System Prompt: ${arienCore.getSystemPrompt().substring(0, 50)}...`,
        '',
        `📁 Config Path: ${require('../../core/config/index.js').configManager.getConfigPath()}`
      ].join('\n');

      return {
        success: true,
        message: statusInfo
      };
    }
  },

  history: {
    name: 'history',
    description: 'Show or manage chat history',
    usage: '/history [clear|list]',
    aliases: ['hist'],
    handler: (args: string[]) => {
      const action = args[0]?.toLowerCase();

      if (action === 'clear') {
        arienCore.clearChatHistory();
        return {
          success: true,
          message: '🗑️ Chat history cleared',
          data: { action: 'clear_history' }
        };
      }

      const history = arienCore.getChatHistory();
      
      if (history.length === 0) {
        return {
          success: true,
          message: '📝 No chat history found'
        };
      }

      const historyList = history.slice(0, 10).map((session, index) => 
        `${index + 1}. ${session.title} (${session.provider}/${session.model}) - ${new Date(session.createdAt).toLocaleDateString()}`
      ).join('\n');

      return {
        success: true,
        message: `📝 Recent Chat History:\n\n${historyList}${
          history.length > 10 ? `\n\n... and ${history.length - 10} more sessions` : ''
        }`
      };
    }
  },

  providers: {
    name: 'providers',
    description: 'List available providers',
    usage: '/providers',
    aliases: ['provider', 'p'],
    handler: () => {
      const currentProvider = arienCore.getCurrentProvider();
      const providerList = PROVIDERS.map(provider => {
        const isAuthenticated = arienCore.isProviderAuthenticated(provider.id);
        const isCurrent = provider.id === currentProvider;
        const status = isAuthenticated ? '✅' : '❌';
        const current = isCurrent ? '→' : ' ';
        
        return `${current} ${status} ${provider.id.padEnd(12)} - ${provider.name} (${provider.models.length} models)`;
      }).join('\n');

      return {
        success: true,
        message: `🔗 Available Providers:\n\n${providerList}\n\n✅ = Authenticated, ❌ = Not authenticated, → = Current`
      };
    }
  },

  models: {
    name: 'models',
    description: 'List available models for current or specified provider',
    usage: '/models [provider-id]',
    aliases: ['model', 'm'],
    handler: (args: string[]) => {
      const providerId = args[0] || arienCore.getCurrentProvider();
      
      if (!providerId) {
        return {
          success: false,
          message: '❌ No provider specified and no current provider set'
        };
      }

      const provider = PROVIDERS.find(p => p.id === providerId);
      
      if (!provider) {
        return {
          success: false,
          message: `❌ Provider not found: ${providerId}`
        };
      }

      const currentModel = arienCore.getCurrentModel();
      const modelList = provider.models.map(model => {
        const isCurrent = model.id === currentModel && providerId === arienCore.getCurrentProvider();
        const current = isCurrent ? '→' : ' ';
        const tokens = model.maxTokens.toLocaleString();
        const functions = model.supportsFunctionCalling ? '🔧' : '  ';
        
        return `${current} ${functions} ${model.id.padEnd(25)} - ${model.name} (${tokens} tokens)`;
      }).join('\n');

      return {
        success: true,
        message: `🤖 Models for ${provider.name}:\n\n${modelList}\n\n🔧 = Supports function calling, → = Current model`
      };
    }
  },

  save: {
    name: 'save',
    description: 'Save current chat session',
    usage: '/save [title]',
    aliases: ['s'],
    handler: (args: string[]) => {
      const title = args.join(' ') || `Chat ${new Date().toLocaleDateString()}`;
      
      return {
        success: true,
        message: `💾 Chat session saved: ${title}`,
        data: { action: 'save_session', title }
      };
    }
  },

  exit: {
    name: 'exit',
    description: 'Exit the application',
    usage: '/exit',
    aliases: ['quit', 'q'],
    handler: () => {
      return {
        success: true,
        message: '👋 Goodbye!',
        data: { action: 'exit' }
      };
    }
  },

  version: {
    name: 'version',
    description: 'Show version information',
    usage: '/version',
    aliases: ['v'],
    handler: () => {
      const packageJson = require('../../../package.json');
      
      return {
        success: true,
        message: `🚀 Arien AI CLI v${packageJson.version}\n\nBuilt with TypeScript, React, and Ink\nLicense: ${packageJson.license}`
      };
    }
  }
};

/**
 * Parse and execute a command
 */
export const executeCommand = async (input: string): Promise<CommandResult> => {
  if (!input.startsWith('/')) {
    return {
      success: false,
      message: 'Commands must start with /'
    };
  }

  const parts = input.slice(1).trim().split(/\s+/);
  const commandName = parts[0].toLowerCase();
  const args = parts.slice(1);

  // Find command by name or alias
  const command = commands[commandName] || Object.values(commands).find(cmd => 
    cmd.aliases?.includes(commandName)
  );

  if (!command) {
    return {
      success: false,
      message: `❌ Unknown command: ${commandName}. Type /help for available commands.`
    };
  }

  try {
    return await command.handler(args);
  } catch (error) {
    return {
      success: false,
      message: `❌ Command execution failed: ${error instanceof Error ? error.message : String(error)}`
    };
  }
};

/**
 * Get command suggestions based on partial input
 */
export const getCommandSuggestions = (partial: string): string[] => {
  if (!partial.startsWith('/')) {
    return [];
  }

  const commandPart = partial.slice(1).toLowerCase();
  const suggestions: string[] = [];

  // Add exact matches first
  for (const [name, command] of Object.entries(commands)) {
    if (name.startsWith(commandPart)) {
      suggestions.push(`/${name}`);
    }
    
    // Check aliases
    if (command.aliases) {
      for (const alias of command.aliases) {
        if (alias.startsWith(commandPart)) {
          suggestions.push(`/${alias}`);
        }
      }
    }
  }

  return suggestions.slice(0, 5); // Limit to 5 suggestions
};
