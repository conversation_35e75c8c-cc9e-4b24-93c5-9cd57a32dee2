/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { useState, useEffect, useCallback } from 'react';
import { arienCore } from '../../core/index.js';
import { ChatMessage, LLMResponse, AuthConfig } from '../../core/types/index.js';

export interface UseArienCoreReturn {
  // Authentication
  authenticateProvider: (providerId: string, authConfig: AuthConfig) => Promise<boolean>;
  isProviderAuthenticated: (providerId: string) => boolean;
  
  // Provider management
  currentProvider: string | undefined;
  currentModel: string | undefined;
  setCurrentProvider: (providerId: string, modelId?: string) => void;
  
  // Theme management
  currentTheme: string;
  setTheme: (themeId: string) => void;
  
  // Chat functionality
  chat: (
    messages: ChatMessage[],
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      enableFunctions?: boolean;
    }
  ) => Promise<LLMResponse>;
  
  // System prompt
  systemPrompt: string;
  setSystemPrompt: (prompt: string) => void;
  
  // Chat history
  chatHistory: any[];
  addChatSession: (session: any) => void;
  clearChatHistory: () => void;
  
  // Utility
  reset: () => void;
}

/**
 * Custom hook for interacting with ArienCore
 * Provides reactive state management for the core functionality
 */
export const useArienCore = (): UseArienCoreReturn => {
  const [currentProvider, setCurrentProviderState] = useState<string | undefined>(
    arienCore.getCurrentProvider()
  );
  const [currentModel, setCurrentModelState] = useState<string | undefined>(
    arienCore.getCurrentModel()
  );
  const [currentTheme, setCurrentThemeState] = useState<string>(
    arienCore.getTheme()
  );
  const [systemPrompt, setSystemPromptState] = useState<string>(
    arienCore.getSystemPrompt()
  );
  const [chatHistory, setChatHistoryState] = useState<any[]>(
    arienCore.getChatHistory()
  );

  // Sync state with core on mount
  useEffect(() => {
    setCurrentProviderState(arienCore.getCurrentProvider());
    setCurrentModelState(arienCore.getCurrentModel());
    setCurrentThemeState(arienCore.getTheme());
    setSystemPromptState(arienCore.getSystemPrompt());
    setChatHistoryState(arienCore.getChatHistory());
  }, []);

  const authenticateProvider = useCallback(async (providerId: string, authConfig: AuthConfig): Promise<boolean> => {
    const result = await arienCore.authenticateProvider(providerId, authConfig);
    if (result) {
      setCurrentProviderState(arienCore.getCurrentProvider());
      setCurrentModelState(arienCore.getCurrentModel());
    }
    return result;
  }, []);

  const isProviderAuthenticated = useCallback((providerId: string): boolean => {
    return arienCore.isProviderAuthenticated(providerId);
  }, []);

  const setCurrentProvider = useCallback((providerId: string, modelId?: string): void => {
    arienCore.setCurrentProvider(providerId, modelId);
    setCurrentProviderState(providerId);
    if (modelId) {
      setCurrentModelState(modelId);
    }
  }, []);

  const setTheme = useCallback((themeId: string): void => {
    arienCore.setTheme(themeId);
    setCurrentThemeState(themeId);
  }, []);

  const chat = useCallback(async (
    messages: ChatMessage[],
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      enableFunctions?: boolean;
    }
  ): Promise<LLMResponse> => {
    return await arienCore.chat(messages, undefined, undefined, options);
  }, []);

  const setSystemPrompt = useCallback((prompt: string): void => {
    arienCore.setSystemPrompt(prompt);
    setSystemPromptState(prompt);
  }, []);

  const addChatSession = useCallback((session: any): void => {
    arienCore.addChatSession(session);
    setChatHistoryState(arienCore.getChatHistory());
  }, []);

  const clearChatHistory = useCallback((): void => {
    arienCore.clearChatHistory();
    setChatHistoryState([]);
  }, []);

  const reset = useCallback((): void => {
    arienCore.reset();
    setCurrentProviderState(undefined);
    setCurrentModelState(undefined);
    setCurrentThemeState(arienCore.getTheme());
    setSystemPromptState(arienCore.getSystemPrompt());
    setChatHistoryState([]);
  }, []);

  return {
    // Authentication
    authenticateProvider,
    isProviderAuthenticated,
    
    // Provider management
    currentProvider,
    currentModel,
    setCurrentProvider,
    
    // Theme management
    currentTheme,
    setTheme,
    
    // Chat functionality
    chat,
    
    // System prompt
    systemPrompt,
    setSystemPrompt,
    
    // Chat history
    chatHistory,
    addChatSession,
    clearChatHistory,
    
    // Utility
    reset
  };
};
