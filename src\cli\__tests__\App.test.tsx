/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { render } from 'ink-testing-library';
import { App } from '../App.js';

// Mock the core module
jest.mock('../../core/index.js', () => ({
  arienCore: {
    getTheme: jest.fn().mockReturnValue('cyberpunk'),
    getCurrentProvider: jest.fn(),
    getCurrentModel: jest.fn(),
    isProviderAuthenticated: jest.fn(),
    setCurrentProvider: jest.fn(),
    setTheme: jest.fn()
  }
}));

// Mock the screen components
jest.mock('../screens/AuthScreen.js', () => ({
  AuthScreen: ({ onAuthenticated, onError, setLoading }: any) => {
    React.useEffect(() => {
      // Simulate authentication after a short delay
      setTimeout(() => {
        onAuthenticated('openai', 'gpt-4o');
      }, 100);
    }, [onAuthenticated]);
    
    return React.createElement('div', { 'data-testid': 'auth-screen' }, 'Auth Screen');
  }
}));

jest.mock('../screens/ThemeSelectionScreen.js', () => ({
  ThemeSelectionScreen: ({ onThemeSelected }: any) => {
    React.useEffect(() => {
      // Simulate theme selection after a short delay
      setTimeout(() => {
        onThemeSelected('matrix');
      }, 100);
    }, [onThemeSelected]);
    
    return React.createElement('div', { 'data-testid': 'theme-screen' }, 'Theme Selection Screen');
  }
}));

jest.mock('../screens/ChatScreen.js', () => ({
  ChatScreen: ({ provider, model, theme }: any) => {
    return React.createElement('div', { 
      'data-testid': 'chat-screen',
      'data-provider': provider,
      'data-model': model,
      'data-theme': theme
    }, 'Chat Screen');
  }
}));

jest.mock('../screens/LoadingScreen.js', () => ({
  LoadingScreen: () => {
    return React.createElement('div', { 'data-testid': 'loading-screen' }, 'Loading...');
  }
}));

jest.mock('../components/ErrorBoundary.js', () => ({
  ErrorBoundary: ({ children }: any) => {
    return React.createElement('div', { 'data-testid': 'error-boundary' }, children);
  }
}));

describe('App Component', () => {
  let mockArienCore: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Get the mocked arienCore
    const { arienCore } = require('../../core/index.js');
    mockArienCore = arienCore;
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('should render auth screen when not authenticated', () => {
      mockArienCore.getCurrentProvider.mockReturnValue(null);
      mockArienCore.isProviderAuthenticated.mockReturnValue(false);

      const { getByTestId } = render(React.createElement(App));
      
      expect(getByTestId('error-boundary')).toBeDefined();
      expect(getByTestId('auth-screen')).toBeDefined();
    });

    it('should render theme selection when already authenticated', () => {
      mockArienCore.getCurrentProvider.mockReturnValue('openai');
      mockArienCore.getCurrentModel.mockReturnValue('gpt-4o');
      mockArienCore.isProviderAuthenticated.mockReturnValue(true);

      const { getByTestId } = render(React.createElement(App));
      
      expect(getByTestId('theme-screen')).toBeDefined();
    });
  });

  describe('Authentication Flow', () => {
    it('should handle successful authentication', async () => {
      mockArienCore.getCurrentProvider.mockReturnValue(null);
      mockArienCore.isProviderAuthenticated.mockReturnValue(false);

      const { getByTestId, rerender } = render(React.createElement(App));
      
      // Initially should show auth screen
      expect(getByTestId('auth-screen')).toBeDefined();
      
      // Wait for authentication to complete
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Should call setCurrentProvider
      expect(mockArienCore.setCurrentProvider).toHaveBeenCalledWith('openai', 'gpt-4o');
    });
  });

  describe('Theme Selection Flow', () => {
    it('should handle theme selection', async () => {
      mockArienCore.getCurrentProvider.mockReturnValue('openai');
      mockArienCore.getCurrentModel.mockReturnValue('gpt-4o');
      mockArienCore.isProviderAuthenticated.mockReturnValue(true);

      const { getByTestId } = render(React.createElement(App));
      
      // Initially should show theme screen
      expect(getByTestId('theme-screen')).toBeDefined();
      
      // Wait for theme selection to complete
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Should call setTheme
      expect(mockArienCore.setTheme).toHaveBeenCalledWith('matrix');
    });
  });

  describe('Loading State', () => {
    it('should show loading screen when loading', () => {
      mockArienCore.getCurrentProvider.mockReturnValue(null);
      mockArienCore.isProviderAuthenticated.mockReturnValue(false);

      // Create a custom App component that starts in loading state
      const LoadingApp = () => {
        const [isLoading, setIsLoading] = React.useState(true);
        
        React.useEffect(() => {
          setTimeout(() => setIsLoading(false), 100);
        }, []);
        
        if (isLoading) {
          return React.createElement('div', { 'data-testid': 'loading-screen' }, 'Loading...');
        }
        
        return React.createElement(App);
      };

      const { getByTestId } = render(React.createElement(LoadingApp));
      
      expect(getByTestId('loading-screen')).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should display error messages', () => {
      mockArienCore.getCurrentProvider.mockReturnValue(null);
      mockArienCore.isProviderAuthenticated.mockReturnValue(false);

      // Create a custom App component that has an error
      const ErrorApp = () => {
        const [error, setError] = React.useState<string | undefined>('Test error message');
        
        return React.createElement('div', { 'data-testid': 'error-boundary' }, [
          React.createElement('div', { key: 'content', 'data-testid': 'auth-screen' }, 'Auth Screen'),
          error && React.createElement('div', { 
            key: 'error', 
            'data-testid': 'error-message',
            style: { color: 'red' }
          }, `Error: ${error}`)
        ]);
      };

      const { getByTestId } = render(React.createElement(ErrorApp));
      
      expect(getByTestId('error-message')).toBeDefined();
    });
  });

  describe('Screen Navigation', () => {
    it('should navigate through all screens in correct order', async () => {
      // Start with unauthenticated state
      mockArienCore.getCurrentProvider.mockReturnValue(null);
      mockArienCore.isProviderAuthenticated.mockReturnValue(false);

      const { getByTestId, rerender } = render(React.createElement(App));
      
      // Should start with auth screen
      expect(getByTestId('auth-screen')).toBeDefined();
      
      // Wait for authentication
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Mock authenticated state and rerender
      mockArienCore.getCurrentProvider.mockReturnValue('openai');
      mockArienCore.getCurrentModel.mockReturnValue('gpt-4o');
      mockArienCore.isProviderAuthenticated.mockReturnValue(true);
      
      rerender(React.createElement(App));
      
      // Should now show theme screen
      expect(getByTestId('theme-screen')).toBeDefined();
      
      // Wait for theme selection
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Rerender to show chat screen
      rerender(React.createElement(App));
    });
  });

  describe('Props Passing', () => {
    it('should pass correct props to ChatScreen', () => {
      // Mock state where we should be in chat screen
      mockArienCore.getCurrentProvider.mockReturnValue('anthropic');
      mockArienCore.getCurrentModel.mockReturnValue('claude-3-5-sonnet');
      mockArienCore.isProviderAuthenticated.mockReturnValue(true);
      mockArienCore.getTheme.mockReturnValue('ocean');

      // Create a component that simulates being in chat state
      const ChatApp = () => {
        return React.createElement('div', { 'data-testid': 'error-boundary' }, 
          React.createElement('div', {
            'data-testid': 'chat-screen',
            'data-provider': 'anthropic',
            'data-model': 'claude-3-5-sonnet',
            'data-theme': 'ocean'
          }, 'Chat Screen')
        );
      };

      const { getByTestId } = render(React.createElement(ChatApp));
      
      const chatScreen = getByTestId('chat-screen');
      expect(chatScreen.getAttribute('data-provider')).toBe('anthropic');
      expect(chatScreen.getAttribute('data-model')).toBe('claude-3-5-sonnet');
      expect(chatScreen.getAttribute('data-theme')).toBe('ocean');
    });
  });
});
