/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { createContext, useContext, ReactNode } from 'react';
import { useTheme, UseThemeReturn } from '../hooks/useTheme.js';

const ThemeContext = createContext<UseThemeReturn | undefined>(undefined);

export interface ThemeProviderProps {
  children: ReactNode;
}

/**
 * Theme context provider component
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const themeState = useTheme();

  return (
    <ThemeContext.Provider value={themeState}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Hook to use theme context
 */
export const useThemeContext = (): UseThemeReturn => {
  const context = useContext(ThemeContext);
  
  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  
  return context;
};
