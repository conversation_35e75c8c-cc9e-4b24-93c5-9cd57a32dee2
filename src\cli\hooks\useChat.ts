/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { useState, useCallback, useRef } from 'react';
import { ChatMessage, LLMResponse } from '../../core/types/index.js';
import { arienCore } from '../../core/index.js';
import { nanoid } from 'nanoid';

export interface UseChatReturn {
  // Chat state
  messages: ChatMessage[];
  isTyping: boolean;
  error: string | null;
  
  // Chat actions
  sendMessage: (content: string, options?: {
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
    enableFunctions?: boolean;
  }) => Promise<void>;
  addMessage: (message: ChatMessage) => void;
  clearMessages: () => void;
  
  // Message utilities
  createUserMessage: (content: string) => ChatMessage;
  createAssistantMessage: (content: string, metadata?: any) => ChatMessage;
  createSystemMessage: (content: string) => ChatMessage;
  
  // Chat session management
  saveSession: (title?: string) => void;
  loadSession: (sessionId: string) => void;
  
  // Error handling
  clearError: () => void;
  
  // Statistics
  messageCount: number;
  tokenUsage: {
    totalInputTokens: number;
    totalOutputTokens: number;
    totalTokens: number;
  };
}

/**
 * Custom hook for chat management
 * Provides reactive chat state and messaging functionality
 */
export const useChat = (): UseChatReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const createUserMessage = useCallback((content: string): ChatMessage => {
    return {
      id: nanoid(),
      role: 'user',
      content,
      timestamp: new Date()
    };
  }, []);

  const createAssistantMessage = useCallback((content: string, metadata?: any): ChatMessage => {
    return {
      id: nanoid(),
      role: 'assistant',
      content,
      timestamp: new Date(),
      metadata
    };
  }, []);

  const createSystemMessage = useCallback((content: string): ChatMessage => {
    return {
      id: nanoid(),
      role: 'system',
      content,
      timestamp: new Date()
    };
  }, []);

  const addMessage = useCallback((message: ChatMessage): void => {
    setMessages(prev => [...prev, message]);
  }, []);

  const clearMessages = useCallback((): void => {
    setMessages([]);
    setError(null);
  }, []);

  const clearError = useCallback((): void => {
    setError(null);
  }, []);

  const sendMessage = useCallback(async (
    content: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      enableFunctions?: boolean;
    }
  ): Promise<void> => {
    if (!content.trim() || isTyping) {
      return;
    }

    // Clear any previous errors
    setError(null);

    // Create user message
    const userMessage = createUserMessage(content.trim());
    
    // Add user message to chat
    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    try {
      // Cancel any previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      // Create new abort controller
      abortControllerRef.current = new AbortController();

      // Send chat request
      const response: LLMResponse = await arienCore.chat(
        [...messages, userMessage],
        undefined, // Use current provider
        undefined, // Use current model
        options
      );

      // Create assistant message
      const assistantMessage = createAssistantMessage(response.content, {
        provider: response.provider,
        model: response.model,
        tokens: response.usage,
        functionCalls: response.functionCalls
      });

      // Add assistant message to chat
      setMessages(prev => [...prev, assistantMessage]);

    } catch (err: any) {
      // Handle errors
      if (err.name === 'AbortError') {
        // Request was cancelled, don't show error
        return;
      }
      
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(`Chat error: ${errorMessage}`);
      
      // Optionally add error message to chat
      const errorChatMessage = createSystemMessage(`Error: ${errorMessage}`);
      setMessages(prev => [...prev, errorChatMessage]);
      
    } finally {
      setIsTyping(false);
      abortControllerRef.current = null;
    }
  }, [messages, isTyping, createUserMessage, createAssistantMessage, createSystemMessage]);

  const saveSession = useCallback((title?: string): void => {
    if (messages.length === 0) {
      return;
    }

    const session = {
      id: nanoid(),
      title: title || `Chat ${new Date().toLocaleDateString()}`,
      messages: [...messages],
      createdAt: new Date(),
      updatedAt: new Date(),
      provider: arienCore.getCurrentProvider() || 'unknown',
      model: arienCore.getCurrentModel() || 'unknown',
      systemPrompt: arienCore.getSystemPrompt()
    };

    arienCore.addChatSession(session);
  }, [messages]);

  const loadSession = useCallback((sessionId: string): void => {
    const history = arienCore.getChatHistory();
    const session = history.find(s => s.id === sessionId);
    
    if (session) {
      setMessages(session.messages || []);
      setError(null);
    }
  }, []);

  // Calculate token usage statistics
  const tokenUsage = {
    totalInputTokens: messages.reduce((total, msg) => {
      return total + (msg.metadata?.tokens?.inputTokens || 0);
    }, 0),
    totalOutputTokens: messages.reduce((total, msg) => {
      return total + (msg.metadata?.tokens?.outputTokens || 0);
    }, 0),
    get totalTokens() {
      return this.totalInputTokens + this.totalOutputTokens;
    }
  };

  return {
    // Chat state
    messages,
    isTyping,
    error,
    
    // Chat actions
    sendMessage,
    addMessage,
    clearMessages,
    
    // Message utilities
    createUserMessage,
    createAssistantMessage,
    createSystemMessage,
    
    // Chat session management
    saveSession,
    loadSession,
    
    // Error handling
    clearError,
    
    // Statistics
    messageCount: messages.length,
    tokenUsage
  };
};
