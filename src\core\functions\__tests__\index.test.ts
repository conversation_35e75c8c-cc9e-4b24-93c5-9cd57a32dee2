/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { FunctionManager } from '../index.js';
import { BuiltinFunction } from '../../types/index.js';

// Mock the function modules
jest.mock('../system.js', () => ({
  systemFunctions: [
    {
      name: 'mock_system_function',
      description: 'Mock system function',
      parameters: {
        type: 'object',
        properties: {
          command: { type: 'string', description: 'Command to execute' }
        },
        required: ['command']
      },
      handler: async (args: any) => `System: ${args.command}`
    }
  ]
}));

jest.mock('../web.js', () => ({
  webFunctions: [
    {
      name: 'mock_web_function',
      description: 'Mock web function',
      parameters: {
        type: 'object',
        properties: {
          url: { type: 'string', description: 'URL to fetch' }
        },
        required: ['url']
      },
      handler: async (args: any) => `Web: ${args.url}`
    }
  ]
}));

jest.mock('../file.js', () => ({
  fileFunctions: []
}));

jest.mock('../utility.js', () => ({
  utilityFunctions: []
}));

describe('FunctionManager', () => {
  let functionManager: FunctionManager;

  beforeEach(() => {
    functionManager = new FunctionManager();
  });

  describe('Initialization', () => {
    it('should register default functions on initialization', () => {
      const allFunctions = functionManager.getAllFunctions();
      
      expect(allFunctions).toHaveLength(2); // system + web mocked functions
      expect(allFunctions.some(f => f.name === 'mock_system_function')).toBe(true);
      expect(allFunctions.some(f => f.name === 'mock_web_function')).toBe(true);
    });
  });

  describe('Function Registration', () => {
    it('should register a new function', () => {
      const testFunction: BuiltinFunction = {
        name: 'test_function',
        description: 'Test function',
        parameters: {
          type: 'object',
          properties: {
            input: { type: 'string', description: 'Test input' }
          },
          required: ['input']
        },
        handler: async (args) => `Test: ${args.input}`
      };

      functionManager.registerFunction(testFunction);
      
      const retrievedFunction = functionManager.getFunction('test_function');
      expect(retrievedFunction).toEqual(testFunction);
    });

    it('should unregister a function', () => {
      const testFunction: BuiltinFunction = {
        name: 'test_function',
        description: 'Test function',
        parameters: {
          type: 'object',
          properties: {},
          required: []
        },
        handler: async () => 'test'
      };

      functionManager.registerFunction(testFunction);
      expect(functionManager.getFunction('test_function')).toBeDefined();
      
      functionManager.unregisterFunction('test_function');
      expect(functionManager.getFunction('test_function')).toBeUndefined();
    });

    it('should override existing function when registering with same name', () => {
      const originalFunction: BuiltinFunction = {
        name: 'test_function',
        description: 'Original function',
        parameters: { type: 'object', properties: {}, required: [] },
        handler: async () => 'original'
      };

      const newFunction: BuiltinFunction = {
        name: 'test_function',
        description: 'New function',
        parameters: { type: 'object', properties: {}, required: [] },
        handler: async () => 'new'
      };

      functionManager.registerFunction(originalFunction);
      functionManager.registerFunction(newFunction);
      
      const retrievedFunction = functionManager.getFunction('test_function');
      expect(retrievedFunction?.description).toBe('New function');
    });
  });

  describe('Function Retrieval', () => {
    it('should get function by name', () => {
      const systemFunction = functionManager.getFunction('mock_system_function');
      expect(systemFunction).toBeDefined();
      expect(systemFunction?.name).toBe('mock_system_function');
    });

    it('should return undefined for non-existent function', () => {
      const nonExistentFunction = functionManager.getFunction('non_existent');
      expect(nonExistentFunction).toBeUndefined();
    });

    it('should get all functions', () => {
      const allFunctions = functionManager.getAllFunctions();
      expect(Array.isArray(allFunctions)).toBe(true);
      expect(allFunctions.length).toBeGreaterThan(0);
    });

    it('should get functions by category', () => {
      const systemFunctions = functionManager.getFunctionsByCategory('system');
      expect(systemFunctions.some(f => f.name === 'mock_system_function')).toBe(true);
      
      const webFunctions = functionManager.getFunctionsByCategory('web');
      expect(webFunctions.some(f => f.name === 'mock_web_function')).toBe(true);
    });
  });

  describe('Function Execution', () => {
    it('should execute function successfully', async () => {
      const result = await functionManager.executeFunction('mock_system_function', {
        command: 'test command'
      });
      
      expect(result).toBe('System: test command');
    });

    it('should throw error for non-existent function', async () => {
      await expect(functionManager.executeFunction('non_existent', {}))
        .rejects.toThrow("Function 'non_existent' not found");
    });

    it('should handle function execution errors', async () => {
      const errorFunction: BuiltinFunction = {
        name: 'error_function',
        description: 'Function that throws error',
        parameters: { type: 'object', properties: {}, required: [] },
        handler: async () => {
          throw new Error('Test error');
        }
      };

      functionManager.registerFunction(errorFunction);
      
      await expect(functionManager.executeFunction('error_function', {}))
        .rejects.toThrow("Function 'error_function' execution failed: Test error");
    });
  });

  describe('Function Validation', () => {
    beforeEach(() => {
      const testFunction: BuiltinFunction = {
        name: 'validation_test',
        description: 'Function for validation testing',
        parameters: {
          type: 'object',
          properties: {
            requiredString: { type: 'string', description: 'Required string parameter' },
            optionalNumber: { type: 'number', description: 'Optional number parameter' },
            requiredBoolean: { type: 'boolean', description: 'Required boolean parameter' }
          },
          required: ['requiredString', 'requiredBoolean']
        },
        handler: async () => 'test'
      };

      functionManager.registerFunction(testFunction);
    });

    it('should validate function call with valid parameters', () => {
      const result = functionManager.validateFunctionCall('validation_test', {
        requiredString: 'test',
        requiredBoolean: true,
        optionalNumber: 42
      });

      expect(result.valid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should fail validation for missing required parameters', () => {
      const result = functionManager.validateFunctionCall('validation_test', {
        requiredString: 'test'
        // missing requiredBoolean
      });

      expect(result.valid).toBe(false);
      expect(result.error).toBe('Missing required parameter: requiredBoolean');
    });

    it('should fail validation for wrong parameter types', () => {
      const result = functionManager.validateFunctionCall('validation_test', {
        requiredString: 123, // should be string
        requiredBoolean: true
      });

      expect(result.valid).toBe(false);
      expect(result.error).toBe("Parameter 'requiredString' must be a string");
    });

    it('should fail validation for non-existent function', () => {
      const result = functionManager.validateFunctionCall('non_existent', {});

      expect(result.valid).toBe(false);
      expect(result.error).toBe("Function 'non_existent' not found");
    });

    it('should validate different parameter types correctly', () => {
      const arrayFunction: BuiltinFunction = {
        name: 'array_test',
        description: 'Function with array parameter',
        parameters: {
          type: 'object',
          properties: {
            items: { type: 'array', description: 'Array of items' }
          },
          required: ['items']
        },
        handler: async () => 'test'
      };

      functionManager.registerFunction(arrayFunction);

      // Valid array
      let result = functionManager.validateFunctionCall('array_test', {
        items: [1, 2, 3]
      });
      expect(result.valid).toBe(true);

      // Invalid array (not an array)
      result = functionManager.validateFunctionCall('array_test', {
        items: 'not an array'
      });
      expect(result.valid).toBe(false);
      expect(result.error).toBe("Parameter 'items' must be an array");
    });
  });
});
