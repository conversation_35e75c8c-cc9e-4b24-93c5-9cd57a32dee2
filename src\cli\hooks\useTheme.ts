/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { useState, useEffect, useCallback } from 'react';
import { Theme } from '../../core/types/index.js';
import { THEMES } from '../../core/config/themes.js';
import { arienCore } from '../../core/index.js';

export interface UseThemeReturn {
  // Current theme
  currentTheme: Theme;
  currentThemeId: string;
  
  // Available themes
  availableThemes: Theme[];
  
  // Theme management
  setTheme: (themeId: string) => void;
  getThemeById: (themeId: string) => Theme | undefined;
  
  // Theme utilities
  getThemeColors: () => Theme['colors'];
  getThemeStyles: () => Theme['styles'];
  
  // Theme preview
  previewTheme: (themeId: string) => Theme | undefined;
}

/**
 * Custom hook for theme management
 * Provides reactive theme state and utilities
 */
export const useTheme = (): UseThemeReturn => {
  const [currentThemeId, setCurrentThemeId] = useState<string>(arienCore.getTheme());
  const [currentTheme, setCurrentTheme] = useState<Theme>(() => {
    const themeId = arienCore.getTheme();
    return THEMES.find(theme => theme.id === themeId) || THEMES[0];
  });

  // Update theme when themeId changes
  useEffect(() => {
    const theme = THEMES.find(t => t.id === currentThemeId) || THEMES[0];
    setCurrentTheme(theme);
  }, [currentThemeId]);

  // Sync with core theme changes
  useEffect(() => {
    const coreThemeId = arienCore.getTheme();
    if (coreThemeId !== currentThemeId) {
      setCurrentThemeId(coreThemeId);
    }
  }, [currentThemeId]);

  const setTheme = useCallback((themeId: string): void => {
    const theme = THEMES.find(t => t.id === themeId);
    if (theme) {
      arienCore.setTheme(themeId);
      setCurrentThemeId(themeId);
    }
  }, []);

  const getThemeById = useCallback((themeId: string): Theme | undefined => {
    return THEMES.find(theme => theme.id === themeId);
  }, []);

  const getThemeColors = useCallback((): Theme['colors'] => {
    return currentTheme.colors;
  }, [currentTheme]);

  const getThemeStyles = useCallback((): Theme['styles'] => {
    return currentTheme.styles;
  }, [currentTheme]);

  const previewTheme = useCallback((themeId: string): Theme | undefined => {
    return THEMES.find(theme => theme.id === themeId);
  }, []);

  return {
    // Current theme
    currentTheme,
    currentThemeId,
    
    // Available themes
    availableThemes: THEMES,
    
    // Theme management
    setTheme,
    getThemeById,
    
    // Theme utilities
    getThemeColors,
    getThemeStyles,
    
    // Theme preview
    previewTheme
  };
};
